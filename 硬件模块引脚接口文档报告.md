# 硬件模块引脚接口文档报告

## 项目概述
- **项目名称**: 基于MSPM0G3507的智能循迹小车系统
- **主控芯片**: MSPM0G3507 (LQFP-64封装)
- **主频**: 80MHz
- **开发板**: LP-MSPM0G3507 LaunchPad

## 1. 模块清单

| 模块名称 | 功能描述 | 接口类型 |
|---------|---------|---------|
| 电机驱动模块 | 双电机PWM控制和方向控制 | PWM + GPIO |
| 编码器模块 | 双路增量式编码器信号采集 | GPIO中断 |
| 灰度传感器模块 | 8路灰度传感器阵列 | ADC + GPIO地址选择 |
| OLED显示模块 | 128x64 OLED显示屏 | I2C/SPI |
| 按键模块 | 用户输入控制 | GPIO输入 |
| 串口通信模块 | 调试和数据传输 | UART |
| LED指示模块 | 状态指示 | GPIO输出 |
| 调试接口模块 | SWD调试接口 | SWD |

## 2. 引脚接口详情

### 2.1 电机驱动模块

#### PWM输出引脚
| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 |
|---------|---------|---------|---------|---------|---------|
| PWM_CCP0 | PB4 | 17 | 电机A PWM控制信号 | 输出 | 3.3V |
| PWM_CCP1 | PB5 | 18 | 电机B PWM控制信号 | 输出 | 3.3V |

#### 方向控制引脚
| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 |
|---------|---------|---------|---------|---------|---------|
| AIN1 | PA14 | 7 | 电机A方向控制1 | 输出 | 3.3V |
| AIN2 | PA13 | 6 | 电机A方向控制2 | 输出 | 3.3V |
| BIN1 | PA16 | 9 | 电机B方向控制1 | 输出 | 3.3V |
| BIN2 | PA17 | 10 | 电机B方向控制2 | 输出 | 3.3V |

**配置要求**:
- PWM频率: 由TIMA1定时器控制，计数值8000
- PWM分辨率: 13位 (0-8000)
- 方向控制逻辑: 高低电平组合控制电机正反转

### 2.2 编码器模块

#### 编码器A (左电机)
| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 | 特殊配置 |
|---------|---------|---------|---------|---------|---------|---------|
| E1A | PA25 | 26 | 编码器A相位A | 输入 | 3.3V | 上升沿中断 |
| E1B | PA26 | 30 | 编码器A相位B | 输入 | 3.3V | 上升沿中断 |

#### 编码器B (右电机)
| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 | 特殊配置 |
|---------|---------|---------|---------|---------|---------|---------|
| E2A | PB20 | 19 | 编码器B相位A | 输入 | 3.3V | 上升沿中断 |
| E2B | PB24 | 23 | 编码器B相位B | 输入 | 3.3V | 上升沿中断 |

**配置要求**:
- 中断优先级: 0 (最高优先级)
- 中断类型: 上升沿触发
- 编码器类型: 增量式正交编码器
- 分辨率: 由具体编码器型号决定

### 2.3 灰度传感器模块

#### ADC采集引脚
| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 |
|---------|---------|---------|---------|---------|---------|
| ADC_IN | PA27 | - | 灰度传感器模拟信号输入 | 输入 | 0-3.3V |

#### 地址选择引脚
| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 |
|---------|---------|---------|---------|---------|---------|
| ADDR0 | PB0 | 47 | 传感器地址选择位0 | 输出 | 3.3V |
| ADDR1 | PB1 | 48 | 传感器地址选择位1 | 输出 | 3.3V |
| ADDR2 | PB2 | 50 | 传感器地址选择位2 | 输出 | 3.3V |

**配置要求**:
- ADC分辨率: 12位 (0-4095)
- 采样时间: 1μs
- 参考电压: VDDA (3.3V)
- 地址选择: 3位二进制编码，支持8路传感器切换
- 采样方式: 软件触发，连续采样模式

### 2.4 OLED显示模块

#### I2C接口
| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 |
|---------|---------|---------|---------|---------|---------|
| SDA | PA28 | 3 | I2C数据线 | 双向 | 3.3V |
| SCL | PA31 | 6 | I2C时钟线 | 输出 | 3.3V |

#### SPI控制引脚 (备用)
| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 |
|---------|---------|---------|---------|---------|---------|
| RES | PB15 | 3 | 复位信号 | 输出 | 3.3V |
| DC | PB14 | 2 | 数据/命令选择 | 输出 | 3.3V |
| CS | PB13 | 1 | 片选信号 | 输出 | 3.3V |

**配置要求**:
- I2C速度: 500kHz
- I2C地址: 由OLED模块决定 (通常0x3C或0x3D)
- 显示分辨率: 128x64像素
- 驱动芯片: SSD1306或兼容芯片

### 2.5 按键模块

| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 | 特殊配置 |
|---------|---------|---------|---------|---------|---------|---------|
| KEY1 | PB16 | 4 | 主控制按键 | 输入 | 3.3V | 内部下拉电阻 |
| KEY2 | PA15 | 8 | 辅助控制按键 | 输入 | 3.3V | 内部下拉电阻 |

**配置要求**:
- 按键类型: 轻触开关
- 内部电阻: 下拉电阻使能
- 按键逻辑: 按下为高电平，松开为低电平
- 防抖处理: 软件防抖

### 2.6 串口通信模块

| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 |
|---------|---------|---------|---------|---------|---------|
| UART_TX | PA10 | 21 | 串口发送 | 输出 | 3.3V |
| UART_RX | PA11 | 22 | 串口接收 | 输入 | 3.3V |

**配置要求**:
- 波特率: 115200 bps
- 数据位: 8位
- 停止位: 1位
- 校验位: 无
- 流控: 无
- 中断: 接收中断使能

### 2.7 LED指示模块

| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 |
|---------|---------|---------|---------|---------|---------|
| LED | PB22 | 21 | 状态指示LED | 输出 | 3.3V |

### 2.8 调试接口模块

| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 |
|---------|---------|---------|---------|---------|---------|
| SWCLK | PA20 | - | SWD时钟 | 输入 | 3.3V |
| SWDIO | PA19 | - | SWD数据 | 双向 | 3.3V |

### 2.9 其他控制引脚

| 引脚名称 | 引脚编号 | 封装引脚 | 功能描述 | 输入/输出 | 电压等级 |
|---------|---------|---------|---------|---------|---------|
| STOP | PA0 | 33 | 停止控制信号 | 输出 | 3.3V |

## 3. 连接关系

### 3.1 电机驱动连接
```
MCU PWM输出 → 电机驱动板PWM输入
MCU方向控制 → 电机驱动板方向输入
电机驱动板 → 直流减速电机
```

### 3.2 编码器连接
```
编码器A相/B相输出 → MCU GPIO中断输入
编码器电源 → 3.3V/5V (根据编码器规格)
```

### 3.3 灰度传感器连接
```
8路灰度传感器 → 模拟开关/多路选择器 → MCU ADC输入
MCU地址选择输出 → 模拟开关地址输入
```

### 3.4 显示模块连接
```
OLED模块 ← I2C总线 ← MCU I2C接口
备用SPI控制线 ← MCU GPIO输出
```

## 4. 接口规范

### 4.1 PWM接口规范
- **定时器**: TIMA1
- **PWM模式**: 边沿对齐向上计数
- **计数范围**: 0-8000
- **PWM频率**: 80MHz / 8000 = 10kHz
- **占空比控制**: 通过DL_Timer_setCaptureCompareValue()函数设置
- **通道配置**:
  - 通道0 (PB4): 电机A PWM
  - 通道1 (PB5): 电机B PWM

### 4.2 I2C接口规范
- **I2C实例**: I2C0
- **工作模式**: 主机模式
- **时钟频率**: 500kHz
- **时钟源分频**: 4分频
- **地址模式**: 7位地址
- **数据传输**: 标准I2C协议
- **OLED地址**: 通常为0x3C (7位地址)

### 4.3 UART接口规范
- **UART实例**: UART0
- **波特率**: 115200 bps
- **时钟频率**: 40MHz
- **数据格式**: 8N1 (8数据位，无校验，1停止位)
- **FIFO配置**: 接收FIFO阈值为1个条目
- **中断配置**: 接收中断使能，优先级1

### 4.4 ADC接口规范
- **ADC实例**: ADC0
- **分辨率**: 12位 (0-4095)
- **参考电压**: VDDA (3.3V)
- **采样时间**: 1μs
- **时钟分频**: 8分频
- **触发方式**: 软件触发
- **转换模式**: 单次转换，重复使能
- **数据格式**: 无符号整数

### 4.5 GPIO中断规范
- **编码器中断**:
  - 中断组: GROUP1
  - 触发方式: 上升沿
  - 优先级: 0 (最高)
  - 中断处理: GROUP1_IRQHandler()

### 4.6 定时器中断规范
- **系统定时器**: TIMG0
- **中断周期**: 125μs (8kHz)
- **预分频**: 32
- **时钟分频**: 2
- **中断优先级**: 默认
- **用途**: 系统任务调度、按键扫描、编码器读取

## 5. 使用注意事项

### 5.1 电机驱动模块注意事项
- **电源要求**: 确保电机驱动板有足够的电源供应
- **PWM频率**: 10kHz PWM频率适合大多数直流电机，避免产生可听噪声
- **方向控制**: AIN1/AIN2和BIN1/BIN2不能同时为高电平，避免短路
- **PWM范围**: PWM值范围0-8000，超出范围可能导致异常
- **电机保护**: 建议在电机驱动电路中加入过流保护

### 5.2 编码器模块注意事项
- **信号质量**: 确保编码器信号线远离强电磁干扰源
- **上拉电阻**: 根据编码器输出类型配置合适的上拉电阻
- **中断处理**: 编码器中断处理函数应尽可能简短，避免影响系统实时性
- **计数溢出**: 注意编码器计数值的溢出处理
- **安装精度**: 编码器安装要牢固，避免机械振动影响计数精度

### 5.3 灰度传感器模块注意事项
- **校准要求**: 使用前必须进行黑白校准，获取准确的阈值
- **环境光影响**: 避免强光直射传感器，影响检测精度
- **地址切换时序**: 切换地址后需要适当延时，确保模拟开关稳定
- **ADC采样**: 每个通道采样8次求平均值，提高测量精度
- **传感器高度**: 传感器距离地面高度要适中，通常2-10mm

### 5.4 OLED显示模块注意事项
- **电源电压**: 确保OLED模块电源电压在规格范围内
- **I2C地址**: 确认OLED模块的I2C地址，避免地址冲突
- **显示刷新**: 避免过于频繁的全屏刷新，影响系统性能
- **字体资源**: 注意字体数据占用的存储空间
- **对比度设置**: 根据环境光线调整显示对比度

### 5.5 按键模块注意事项
- **防抖处理**: 必须进行软件防抖，避免误触发
- **按键响应**: 按键检测周期不宜过短，建议10ms以上
- **长按检测**: 如需长按功能，注意设置合适的长按时间阈值
- **中断vs轮询**: 简单按键建议使用轮询方式，复杂按键可考虑中断

### 5.6 串口通信注意事项
- **波特率匹配**: 确保通信双方波特率一致
- **数据缓冲**: 使用环形缓冲区处理串口数据，避免数据丢失
- **中断处理**: 串口中断处理要快速，避免影响其他任务
- **流控制**: 高速数据传输时考虑使用硬件流控制

### 5.7 系统级注意事项
- **时钟配置**: 系统时钟80MHz，确保各外设时钟配置正确
- **中断优先级**: 合理配置中断优先级，编码器中断优先级最高
- **电源管理**: 注意系统功耗，合理使用低功耗模式
- **EMC设计**: PCB布局时注意电磁兼容性设计
- **调试接口**: 调试时注意SWD引脚的跳线设置

### 5.8 软件架构注意事项
- **任务调度**: 使用125μs定时器进行任务调度
- **数据同步**: 注意中断和主循环之间的数据同步
- **内存管理**: 合理使用静态内存分配，避免动态分配
- **代码优化**: 中断服务程序要简洁高效
- **错误处理**: 添加必要的错误检测和处理机制

## 6. 缺失信息说明

以下信息在当前项目文件中未找到完整定义，建议补充：

1. **具体电机型号和参数**: 电机额定电压、电流、转速等规格
2. **编码器分辨率**: 每转脉冲数(PPR)参数
3. **灰度传感器具体型号**: 传感器阵列的具体型号和电气特性
4. **电机驱动板型号**: 驱动板的具体型号和接口定义
5. **PCB连接器定义**: 各模块间连接器的引脚定义
6. **电源系统设计**: 各模块的供电电压和电流需求
7. **机械安装尺寸**: 各传感器和执行器的安装位置和尺寸要求

## 7. 建议改进

1. **添加硬件原理图**: 补充完整的电路原理图
2. **完善接口文档**: 详细定义各连接器的引脚功能
3. **增加测试程序**: 为每个硬件模块编写独立的测试程序
4. **性能参数测试**: 测试并记录各模块的实际性能参数
5. **故障诊断指南**: 编写硬件故障诊断和排除指南
